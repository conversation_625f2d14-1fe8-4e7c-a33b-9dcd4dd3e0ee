# 海盗王Online - 大坪社区游戏指南

这是一个基于Electron的桌面应用程序，用于展示海盗王Online游戏的详细信息和指南。

## 功能特性

- 🎮 **基础设置** - 游戏基础配置和系统介绍
- ⚔️ **装备系统** - 详细的装备升级流程和强化系统
- 👹 **怪物掉落** - 各种副本和怪物的掉落信息
- 🏰 **副本介绍** - 地狱、黑龙洞、三幻等副本攻略
- ✨ **觉醒功能** - 装备觉醒系统说明
- 😈 **恶魔世界** - 高级副本介绍
- 🏝️ **遗失岛屿** - PK抢夺战玩法

## 界面特色

- 美观的现代化界面设计
- 响应式布局，支持不同屏幕尺寸
- 侧边导航栏，便于快速切换内容
- 卡片式布局，信息展示清晰
- 使用游戏相关图片作为背景和装饰

## 安装和运行

### 方法一：作为Electron应用运行

1. 确保已安装Node.js (版本 >= 14)
2. 安装依赖：
   ```bash
   npm install
   ```
3. 运行应用：
   ```bash
   npm start
   ```

### 方法二：直接在浏览器中查看

由于应用使用了标准的HTML/CSS/JavaScript技术，您也可以直接在浏览器中打开 `index.html` 文件来查看界面。

## 项目结构

```
├── package.json          # 项目配置文件
├── main.js               # Electron主进程文件
├── index.html            # 主界面HTML
├── styles.css            # 样式文件
├── script.js             # 前端交互逻辑
├── 图片/                 # 游戏相关图片资源
└── README.md             # 项目说明文档
```

## 技术栈

- **Electron** - 跨平台桌面应用框架
- **HTML5** - 页面结构
- **CSS3** - 样式和动画
- **JavaScript** - 交互逻辑
- **Google Fonts** - 中文字体支持

## 开发说明

- 应用支持热重载，修改代码后会自动刷新
- 使用了现代CSS特性如Grid布局、Flexbox、渐变等
- 图片资源来自项目的"图片"目录
- 响应式设计，适配不同屏幕尺寸

## 构建打包

要构建可分发的应用程序：

```bash
npm run build
```

这将在 `dist` 目录中生成可安装的应用程序文件。

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系大坪社区管理员。
